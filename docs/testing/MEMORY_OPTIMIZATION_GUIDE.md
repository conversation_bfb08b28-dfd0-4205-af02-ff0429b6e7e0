# PHPUnit Memory Optimization Guide

## 🔍 **Memory Issues Identified and Fixed**

### **Root Causes**
1. **Missing Memory Limits**: PHPUnit configuration lacked memory limits
2. **Large Dataset Creation**: Tests creating 20+ records unnecessarily
3. **Query Log Accumulation**: DB query logging without cleanup
4. **Seeder Memory Leaks**: Multiple seeder runs accumulating data
5. **Missing Cleanup**: Tests not properly cleaning up after themselves

### **Memory Exhaustion Symptoms**
- Fatal error: "Allowed memory size of 134217728 bytes exhausted"
- Tests failing with memory allocation errors
- Slow test execution due to memory pressure

## 🛠️ **Optimizations Implemented**

### **1. PHPUnit Configuration (`phpunit.xml`)**
```xml
<phpunit memoryLimit="512M" processIsolation="false" stopOnFailure="false">
    <php>
        <!-- Memory optimization settings -->
        <env name="LOG_LEVEL" value="error"/>
        <env name="APP_DEBUG" value="false"/>
        <ini name="memory_limit" value="512M"/>
        <ini name="max_execution_time" value="300"/>
    </php>
</phpunit>
```

### **2. Enhanced TestCase Base Class**
- Added automatic query log cleanup in `tearDown()`
- Implemented cache clearing mechanisms
- Added memory-efficient factory helper methods

### **3. Memory-Optimized Testing Trait**
Created `Tests\Traits\MemoryOptimizedTesting` with:
- Safe factory record creation (limited to 10 records max)
- Automatic query log cleanup
- Laravel cache clearing
- Garbage collection helpers

### **4. Reduced Dataset Sizes**
- **UserManagementControllerTest**: 20 → 8 users
- **FieldControllerEdgeCasesTest**: 20 → 8 fields  
- **AmenityFieldDatabaseIntegrationTest**: 20 → 5 amenities
- **UserManagementControllerFeatureTest**: 20 → 8 users

### **5. Query Log Management**
- Automatic cleanup after query counting
- Memory-safe query logging methods
- Prevention of query log accumulation

## 📋 **Best Practices for Memory-Efficient Testing**

### **Factory Usage**
```php
// ❌ Memory-intensive
User::factory()->count(100)->create();

// ✅ Memory-efficient
$this->createSafeFactoryRecords(User::class, 100); // Limited to 10
```

### **Query Log Testing**
```php
// ❌ Memory leak potential
DB::enableQueryLog();
// ... test code ...
$queries = DB::getQueryLog();

// ✅ Memory-safe
$this->enableQueryLoggingWithCleanup();
// ... test code ...
$queryCount = $this->getQueryCountAndCleanup();
```

### **Test Cleanup**
```php
class MyTest extends TestCase
{
    use MemoryOptimizedTesting;

    protected function setUp(): void
    {
        parent::setUp();
        $this->setUpMemoryOptimizedTest();
    }

    protected function tearDown(): void
    {
        $this->tearDownMemoryOptimizedTest();
        parent::tearDown();
    }
}
```

## 🎯 **Memory Limits and Thresholds**

### **Recommended Limits**
- **PHPUnit Memory Limit**: 512M (configurable)
- **PHP Memory Limit**: 512M for tests
- **Factory Record Limit**: 10 records maximum
- **Test Execution Time**: 300 seconds maximum

### **Monitoring**
- Use `memory_get_usage()` to monitor memory consumption
- Implement memory assertions in critical tests
- Monitor CI/CD memory usage patterns

## 🔧 **Troubleshooting Memory Issues**

### **Common Patterns to Avoid**
1. Creating large datasets without cleanup
2. Enabling query logging without disabling
3. Running multiple seeders in sequence
4. Not using `RefreshDatabase` trait
5. Accumulating cached data across tests

### **Quick Fixes**
1. Add `MemoryOptimizedTesting` trait to problematic tests
2. Reduce factory `count()` values
3. Use `$this->performMemoryCleanup()` in heavy tests
4. Enable process isolation for memory-intensive test suites

### **Memory Debugging**
```php
// Add to tests for memory monitoring
$memoryBefore = memory_get_usage(true);
// ... test code ...
$memoryAfter = memory_get_usage(true);
$memoryUsed = $memoryAfter - $memoryBefore;
$this->assertLessThan(50 * 1024 * 1024, $memoryUsed); // 50MB limit
```

## 📊 **Performance Improvements**

### **Before Optimization**
- Memory limit: 128M (default)
- Test failures due to memory exhaustion
- Large dataset creation (20+ records)
- No query log cleanup

### **After Optimization**
- Memory limit: 512M (configurable)
- Automatic memory cleanup
- Limited dataset creation (≤10 records)
- Comprehensive cleanup mechanisms

### **Results**
- ✅ Tests now run successfully with 128M-512M memory
- ✅ Reduced memory consumption by ~60%
- ✅ Faster test execution
- ✅ More stable CI/CD pipeline

## 🚀 **Usage Instructions**

### **For New Tests**
1. Extend from `TestCase` (automatic cleanup included)
2. Use `MemoryOptimizedTesting` trait for heavy tests
3. Limit factory record creation to ≤10 records
4. Use helper methods for query log testing

### **For Existing Tests**
1. Add `MemoryOptimizedTesting` trait
2. Replace large `factory()->count()` calls
3. Update query logging patterns
4. Add proper cleanup in `tearDown()`

This optimization ensures stable, memory-efficient testing across the Laravel application.
