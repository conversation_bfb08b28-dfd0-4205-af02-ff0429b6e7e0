<?php

namespace Tests\Traits;

use Illuminate\Support\Facades\DB;

/**
 * Trait for memory-optimized testing patterns
 * 
 * This trait provides methods to help prevent memory leaks and optimize
 * memory usage in PHPUnit tests, especially for Laravel applications.
 */
trait MemoryOptimizedTesting
{
    /**
     * Maximum safe count for factory creation to prevent memory issues
     */
    protected int $maxSafeFactoryCount = 10;

    /**
     * Create a safe number of factory records
     * 
     * @param string $factoryClass The factory class name
     * @param int $count Desired count (will be limited to safe maximum)
     * @param array $attributes Additional attributes
     * @return \Illuminate\Database\Eloquent\Collection
     */
    protected function createSafeFactoryRecords(string $factoryClass, int $count, array $attributes = [])
    {
        $safeCount = min($count, $this->maxSafeFactoryCount);
        return $factoryClass::factory()->count($safeCount)->create($attributes);
    }

    /**
     * Clean up query log to prevent memory accumulation
     */
    protected function cleanupQueryLog(): void
    {
        if (DB::logging()) {
            DB::disableQueryLog();
            DB::flushQueryLog();
        }
    }

    /**
     * Enable query logging with automatic cleanup
     */
    protected function enableQueryLoggingWithCleanup(): void
    {
        $this->cleanupQueryLog();
        DB::enableQueryLog();
    }

    /**
     * Get query count and clean up
     */
    protected function getQueryCountAndCleanup(): int
    {
        $count = count(DB::getQueryLog());
        $this->cleanupQueryLog();
        return $count;
    }

    /**
     * Force garbage collection to free memory
     */
    protected function forceGarbageCollection(): void
    {
        if (function_exists('gc_collect_cycles')) {
            gc_collect_cycles();
        }
    }

    /**
     * Clear Laravel application caches
     */
    protected function clearLaravelCaches(): void
    {
        if (isset($this->app) && $this->app) {
            try {
                // Clear view cache
                if ($this->app->bound('view')) {
                    $this->app['view']->flushState();
                }

                // Clear cache (safely)
                if ($this->app->bound('cache')) {
                    try {
                        $this->app['cache']->flush();
                    } catch (\Exception $e) {
                        // Ignore cache flush errors in tests
                    }
                }

                // Clear events
                if ($this->app->bound('events')) {
                    $this->app['events']->flush();
                }
            } catch (\Exception $e) {
                // Ignore cleanup errors to prevent test failures
            }
        }
    }

    /**
     * Perform comprehensive memory cleanup
     */
    protected function performMemoryCleanup(): void
    {
        $this->cleanupQueryLog();
        $this->clearLaravelCaches();
        $this->forceGarbageCollection();
    }

    /**
     * Set up memory-optimized test environment
     */
    protected function setUpMemoryOptimizedTest(): void
    {
        try {
            // Disable query logging by default
            DB::disableQueryLog();
        } catch (\Exception $e) {
            // Ignore if DB not available
        }
    }

    /**
     * Tear down memory-optimized test environment
     */
    protected function tearDownMemoryOptimizedTest(): void
    {
        try {
            $this->performMemoryCleanup();
        } catch (\Exception $e) {
            // Ignore cleanup errors to prevent test failures
        }
    }
}
