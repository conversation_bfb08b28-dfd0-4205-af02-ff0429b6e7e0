<?php

namespace Tests\Feature\Admin\User;

use App\Http\Controllers\Admin\UserManagementController;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

#[CoversClass(UserManagementController::class)]
class UserManagementControllerFeatureTest extends TestCase
{
    use RefreshDatabase;

    protected User $adminUser;

    protected User $employeeUser;

    protected User $regularUser;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test users with different roles
        $this->adminUser = User::factory()->create([
            'role' => 'admin',
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
        ]);

        $this->employeeUser = User::factory()->create([
            'role' => 'employee',
            'name' => 'Employee User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
        ]);

        $this->regularUser = User::factory()->create([
            'role' => 'user',
            'name' => 'Regular User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
        ]);
    }

    #[Test]
    public function admin_can_access_users_index_page()
    {
        $response = $this->actingAs($this->adminUser)
            ->get(route('admin.users.index'));

        $response->assertStatus(200);
        $response->assertViewIs('admin.users.index');
        $response->assertViewHas('users');
        $response->assertSee('User Management');
    }

    #[Test]
    public function admin_can_access_user_create_page()
    {
        $response = $this->actingAs($this->adminUser)
            ->get(route('admin.users.create'));

        $response->assertStatus(200);
        $response->assertViewIs('admin.users.create');
        $response->assertSee('Create New User');
    }

    #[Test]
    public function admin_can_access_user_show_page()
    {
        $user = User::factory()->create(['role' => 'employee']);

        $response = $this->actingAs($this->adminUser)
            ->get(route('admin.users.show', $user));

        $response->assertStatus(200);
        $response->assertViewIs('admin.users.show');
        $response->assertViewHas('user', $user);
        $response->assertSee($user->name);
        $response->assertSee($user->email);
    }

    #[Test]
    public function admin_can_access_user_edit_page()
    {
        $user = User::factory()->create(['role' => 'employee']);

        $response = $this->actingAs($this->adminUser)
            ->get(route('admin.users.edit', $user));

        $response->assertStatus(200);
        $response->assertViewIs('admin.users.edit');
        $response->assertViewHas('user', $user);
        $response->assertSee('Edit User');
        $response->assertSee($user->name);
    }

    #[Test]
    public function admin_can_create_new_user_via_post_request()
    {
        $userData = [
            'name' => 'New Test User',
            'email' => '<EMAIL>',
            'username' => 'newuser',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'role' => 'employee',
        ];

        $response = $this->actingAs($this->adminUser)
            ->post(route('admin.users.store'), $userData);

        $response->assertStatus(302);
        $this->assertDatabaseHas('users', [
            'name' => 'New Test User',
            'email' => '<EMAIL>',
            'username' => 'newuser',
            'role' => 'employee',
        ]);

        $user = User::where('email', '<EMAIL>')->first();
        $this->assertNotNull($user->email_verified_at); // Auto-verified
        $this->assertTrue(Hash::check('password123', $user->password));

        $response->assertRedirect(route('admin.users.show', $user));
        $response->assertSessionHas('success', 'User created successfully.');
    }

    #[Test]
    public function admin_can_create_user_without_username()
    {
        $userData = [
            'name' => 'User Without Username',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'role' => 'user',
        ];

        $response = $this->actingAs($this->adminUser)
            ->post(route('admin.users.store'), $userData);

        $response->assertStatus(302);
        $this->assertDatabaseHas('users', [
            'name' => 'User Without Username',
            'email' => '<EMAIL>',
            'username' => null,
            'role' => 'user',
        ]);
    }

    #[Test]
    public function admin_can_update_user_via_put_request()
    {
        $user = User::factory()->create([
            'name' => 'Original Name',
            'email' => '<EMAIL>',
            'role' => 'user',
        ]);

        $updateData = [
            'name' => 'Updated Name',
            'email' => '<EMAIL>',
            'username' => 'updateduser',
            'role' => 'employee',
            'account_status' => 'active',
        ];

        $response = $this->actingAs($this->adminUser)
            ->put(route('admin.users.update', $user), $updateData);

        $response->assertStatus(302);
        $response->assertRedirect(route('admin.users.show', $user));
        $response->assertSessionHas('success', 'User updated successfully.');

        $user->refresh();
        $this->assertEquals('Updated Name', $user->name);
        $this->assertEquals('<EMAIL>', $user->email);
        $this->assertEquals('updateduser', $user->username);
        $this->assertEquals('employee', $user->role);
    }

    #[Test]
    public function admin_can_update_user_password()
    {
        $user = User::factory()->create(['role' => 'employee']);
        $oldPasswordHash = $user->password;

        $updateData = [
            'name' => $user->name,
            'email' => $user->email,
            'username' => $user->username,
            'role' => $user->role,
            'account_status' => 'active',
            'password' => 'newpassword123',
            'password_confirmation' => 'newpassword123',
        ];

        $response = $this->actingAs($this->adminUser)
            ->put(route('admin.users.update', $user), $updateData);

        $response->assertStatus(302);
        $user->refresh();
        $this->assertNotEquals($oldPasswordHash, $user->password);
        $this->assertTrue(Hash::check('newpassword123', $user->password));
    }

    #[Test]
    public function admin_can_delete_user_via_delete_request()
    {
        $userToDelete = User::factory()->create(['role' => 'employee']);
        $userName = $userToDelete->name;

        $response = $this->actingAs($this->adminUser)
            ->delete(route('admin.users.destroy', $userToDelete));

        $response->assertStatus(302);
        $response->assertRedirect(route('admin.users.index'));
        $response->assertSessionHas('success', "User '{$userName}' has been deleted successfully.");

        $this->assertDatabaseMissing('users', ['id' => $userToDelete->id]);
    }

    #[Test]
    public function admin_can_update_user_role_via_post_request()
    {
        $user = User::factory()->create(['role' => 'user']);

        $response = $this->actingAs($this->adminUser)
            ->post(route('admin.users.update-role', $user), ['role' => 'employee']);

        $response->assertStatus(302);
        $response->assertSessionHas('success', 'User role updated successfully to employee.');

        $user->refresh();
        $this->assertEquals('employee', $user->role);
    }

    #[Test]
    public function admin_can_lock_user_account()
    {
        $user = User::factory()->create([
            'role' => 'employee',
            'locked_until' => null,
            'failed_login_attempts' => 0,
        ]);

        $response = $this->actingAs($this->adminUser)
            ->post(route('admin.users.lock', $user));

        $response->assertStatus(302);
        $response->assertSessionHas('success', "User '{$user->name}' has been locked for 24 hours.");

        $user->refresh();
        $this->assertTrue($user->isLocked());
        $this->assertEquals(5, $user->failed_login_attempts);
    }

    #[Test]
    public function admin_can_unlock_user_account()
    {
        $user = User::factory()->create([
            'role' => 'employee',
            'locked_until' => now()->addHours(24),
            'failed_login_attempts' => 5,
        ]);

        $response = $this->actingAs($this->adminUser)
            ->post(route('admin.users.unlock', $user));

        $response->assertStatus(302);
        $response->assertSessionHas('success', "User '{$user->name}' has been unlocked successfully.");

        $user->refresh();
        $this->assertFalse($user->isLocked());
        $this->assertEquals(0, $user->failed_login_attempts);
        $this->assertNull($user->locked_until);
    }

    #[Test]
    public function users_index_displays_paginated_users()
    {
        // Create additional users for pagination testing (reduced from 20 to 8 for memory optimization)
        User::factory()->count(8)->create();

        $response = $this->actingAs($this->adminUser)
            ->get(route('admin.users.index'));

        $response->assertStatus(200);
        $response->assertViewHas('users');

        $users = $response->viewData('users');
        $this->assertInstanceOf(\Illuminate\Pagination\LengthAwarePaginator::class, $users);
        $this->assertEquals(15, $users->perPage());
    }

    #[Test]
    public function users_are_ordered_by_created_at_desc_in_index()
    {
        // Clear existing users and create test users with specific timestamps
        User::query()->delete();

        $oldUser = User::factory()->create(['created_at' => now()->subDays(2)]);
        $newUser = User::factory()->create(['created_at' => now()->subDay()]);
        $newestUser = User::factory()->create(['created_at' => now()]);

        $response = $this->actingAs($this->adminUser)
            ->get(route('admin.users.index'));

        $response->assertStatus(200);
        $users = $response->viewData('users');

        // Check that newest user comes first
        $this->assertEquals($newestUser->id, $users->first()->id);
    }
}
