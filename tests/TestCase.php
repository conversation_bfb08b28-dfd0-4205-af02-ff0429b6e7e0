<?php

namespace Tests;

use Illuminate\Foundation\Testing\TestCase as BaseTestCase;
use Illuminate\Support\Facades\DB;

abstract class TestCase extends BaseTestCase
{
    /**
     * Clean up after each test to prevent memory leaks
     */
    protected function tearDown(): void
    {
        // Disable and flush query log to prevent memory accumulation
        if (DB::logging()) {
            DB::disableQueryLog();
            DB::flushQueryLog();
        }

        // Clear any cached data
        if (method_exists($this, 'clearApplicationCache')) {
            $this->clearApplicationCache();
        }

        parent::tearDown();
    }

    /**
     * Clear application cache to prevent memory leaks
     */
    protected function clearApplicationCache(): void
    {
        if ($this->app) {
            // Clear view cache
            if ($this->app->bound('view')) {
                $this->app['view']->flushState();
            }

            // Clear config cache
            if ($this->app->bound('config')) {
                $this->app['config']->set([], []);
            }
        }
    }

    /**
     * Create a limited number of factory records for memory efficiency
     * Use this instead of factory()->count() with large numbers
     */
    protected function createLimitedRecords(string $factory, int $count = 5, array $attributes = []): \Illuminate\Database\Eloquent\Collection
    {
        // Limit to reasonable numbers for memory efficiency
        $safeCount = min($count, 10);

        return $factory::factory()->count($safeCount)->create($attributes);
    }
}
