<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="vendor/phpunit/phpunit/phpunit.xsd"
         bootstrap="vendor/autoload.php"
         colors="true"
         processIsolation="false"
         stopOnFailure="false"
>
    <testsuites>
        <testsuite name="Unit">
            <directory>tests/Unit</directory>
        </testsuite>
        <testsuite name="Feature">
            <directory>tests/Feature</directory>
        </testsuite>
    </testsuites>
    <source>
        <include>
            <directory>app</directory>
            <directory>database/factories</directory>
            <directory>database/seeders</directory>
        </include>
    </source>
    <php>
        <env name="APP_ENV" value="testing"/>
        <env name="APP_MAINTENANCE_DRIVER" value="file"/>
        <env name="BCRYPT_ROUNDS" value="4"/>
        <env name="CACHE_STORE" value="array"/>
        <env name="DB_CONNECTION" value="sqlite"/>
        <env name="DB_DATABASE" value=":memory:"/>
        <env name="MAIL_MAILER" value="array"/>
        <env name="PULSE_ENABLED" value="false"/>
        <env name="QUEUE_CONNECTION" value="sync"/>
        <env name="SESSION_DRIVER" value="array"/>
        <env name="TELESCOPE_ENABLED" value="false"/>
        <!-- Memory optimization settings -->
        <env name="LOG_LEVEL" value="error"/>
        <env name="APP_DEBUG" value="false"/>
        <ini name="memory_limit" value="512M"/>
        <ini name="max_execution_time" value="300"/>
    </php>
    <logging>
        <junit outputFile="ci-runs/phpunit-results.xml"/>
    </logging>
     <coverage ignoreDeprecatedCodeUnits="true"
              disableCodeCoverageIgnore="true">
        <report>
            <clover outputFile="ci-runs/coverage-report/clover.xml"/>
        </report>
    </coverage>
</phpunit>
